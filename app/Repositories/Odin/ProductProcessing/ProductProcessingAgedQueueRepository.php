<?php

namespace App\Repositories\Odin\ProductProcessing;

use App\Jobs\CalculatePotentialRevenueAgedQueue;
use App\Models\AvailableBudget;
use App\Models\Call;
use App\Models\LeadProcessingCallingTimeZoneConfiguration;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessor;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\RecycledLeads\LeadProcessingAged;
use App\Models\Text;
use App\Models\User;
use App\Models\USZipCode;
use App\Models\Voicemail;
use App\Services\AgedQueueService;
use App\Services\DatabaseHelperService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ProductProcessingAgedQueueRepository
{
    const int MAX_DAYS = 30;
    const int MAX_LEADS = 50;
    const string RESERVED_LEAD_ID = 'reserved_lead_id';
    const int SKIP_DELAY_DAYS = 14;
    const int MAX_RECENTLY_CONTACTED_HOURS = 48;

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return LeadProcessingAged
     * @deprecated
     */
    public function createAgedQueue(ConsumerProduct $consumerProduct): LeadProcessingAged
    {
        /** @var LeadProcessingAged $queue*/
        $queue = LeadProcessingAged::query()->firstOrCreate([
            LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id
        ]);

        CalculatePotentialRevenueAgedQueue::dispatch($queue->id);

        /** @var ProductProcessingService $service */
        $service = app(ProductProcessingService::class);

        $service->releaseProduct($consumerProduct, LeadProcessor::systemProcessor());

        return $queue;
    }

    /**
     * @param LeadProcessor $processor
     * @param bool $noLimitOnly
     *
     * @return ConsumerProduct|null
     */
    public function getNextProduct(LeadProcessor $processor, bool $noLimitOnly = false): ?ConsumerProduct
    {
        $query = $this->getQueryForAgedQueue($processor, $noLimitOnly)->limit(self::MAX_LEADS);

        $result = collect(
            DB::select($query->toRawSql())
        )->sortByDesc(fn($queue) => $queue->{LeadProcessingAged::FIELD_RECENCY_POINTS});

        if ($result->isEmpty()) {
            return null;
        }

        $reserved = $result->filter(fn($aged) => !! $aged->{self::RESERVED_LEAD_ID});

        if ($reserved->isNotEmpty()) {
            return ConsumerProduct::query()->find($reserved->first()->{LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID});
        }

        return ConsumerProduct::query()->find($result->first()->{LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID});
    }

    /**
     * @param LeadProcessor $processor
     * @param bool $noLimitOnly
     * @param int[]|null $industriesOverwrite
     * @param int[]|null $utcOffsets
     *
     * @return Builder
     */
    public function getQueryForAgedQueue(LeadProcessor $processor, bool $noLimitOnly = false, ?array $industriesOverwrite = null, ?array $utcOffsets = null): Builder
    {
        $query = $this->getBaseQuery(
            $processor,
            $industriesOverwrite ?? $processor->team->industries->pluck(Industry::FIELD_ID)->toArray()
        );

        if($noLimitOnly) {
            $this->applyNoLimitBudgetFilter($query);
        } else {
            $this->applyBudgetFilter($query);
        }

        $this->applyQueueTimezoneFilter($query, $utcOffsets ?? [$processor->team->primary_utc_offset]);
        $this->applyCallingTimezoneFilter($query);
        $this->applySkippedLeadsFilter($query);
        $this->applyRecentlyWorkedFilter($query);
        $this->applyFullyAllocatedFilter($query);
        $this->orderBy($query);

        return $query;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     *
     * @return bool
     */
    public function skipProduct(ConsumerProduct $consumerProduct, LeadProcessor $processor, string $reason): bool
    {
        /** @var LeadProcessingAged|null $agedQueue */
        $agedQueue = LeadProcessingAged::query()->where(LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)->latest()->first();

        if (!$agedQueue) {
            return false;
        }

        $agedQueue->skipped_at = now();
        $agedQueue->skip_count++;
        $agedQueue->skip_reasons =[...$agedQueue->skip_reasons ?? [], [
            LeadProcessingAged::REASON_PROCESSOR => $processor->id,
            LeadProcessingAged::REASON_TIMESTAMP => now()->timestamp,
            LeadProcessingAged::REASON_REASON => $reason
        ]];

        return $agedQueue->save();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return array
     */
    public function getSkipReasons(ConsumerProduct $consumerProduct): array
    {
        /** @var LeadProcessingAged $queue */
        $queue = LeadProcessingAged::query()->where(LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)->first();

        if (!$queue || !$queue->skip_reasons) {
            return [];
        }

        return collect($queue->skip_reasons)->map(fn(array $reasons) => [
            'processor_name' => LeadProcessor::query()->find(Arr::get($reasons, LeadProcessingAged::REASON_PROCESSOR, 0))?->{LeadProcessor::RELATION_USER}?->{User::FIELD_NAME},
            'timestamp' => Arr::get($reasons, LeadProcessingAged::REASON_TIMESTAMP),
            'reason' => Arr::get($reasons, LeadProcessingAged::REASON_REASON)
        ])->toArray();
    }

    /**
     * @param LeadProcessor $processor
     * @param array $industries
     *
     * @return Builder
     */
    protected function getBaseQuery(LeadProcessor $processor, array $industries): Builder
    {
        return $this->addInitialJoins(LeadProcessingAged::query(), $processor)
            ->select([
                LeadProcessingAged::TABLE . '.*',
                DB::raw(LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_ID . ' AS ' . self::RESERVED_LEAD_ID)]
            )
            ->where(LeadProcessingAged::TABLE . '.' . LeadProcessingAged::CREATED_AT,'>=', now()->subDays(self::MAX_DAYS))
            ->whereNotExists(fn(QueryBuilder $query) => $query->select(DB::raw(1))
                ->from(LeadProcessingReservedLead::TABLE)
                ->whereColumn(
                    LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID,
                    LeadProcessingAged::TABLE . '.' . LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID)
                ->where(LeadProcessingReservedLead::FIELD_PROCESSOR_ID, '!=', $processor->id)
            )
            ->whereIn(IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID, $industries);
    }

    /**
     * @param Builder $query
     * @param LeadProcessor $processor
     *
     * @return Builder
     */
    protected function addInitialJoins(Builder $query, LeadProcessor $processor): Builder
    {
        return $query->join(
            ConsumerProduct::TABLE,
            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID,
            LeadProcessingAged::TABLE . '.' . LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID
        )->join(
            Address::TABLE,
            Address::TABLE . '.' . Address::FIELD_ID,
            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID
        )->join(
            Location::TABLE,
            Location::TABLE . '.' . Location::ID,
            Address::TABLE . '.' . Address::FIELD_ZIP_CODE_LOCATION_ID
        )->join(
            USZipCode::TABLE,
            fn(JoinClause $joinClause) => $joinClause->on(
                USZipCode::TABLE . '.' . USZipCode::FIELD_ZIP_CODE,
                Address::TABLE . '.' . Address::FIELD_ZIP_CODE
            )->where(USZipCode::TABLE . '.' . USZipCode::FIELD_ZIP_TYPE, USZipCode::DEFAULT_ZIP_TYPE)
            ->where(USZipCode::TABLE . '.' . USZipCode::FIELD_CITY_TYPE, USZipCode::DEFAULT_CITY_TYPE)
        )->join(
            ServiceProduct::TABLE,
            ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID,
            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
        )->join(
            IndustryService::TABLE,
            IndustryService::TABLE . '.' . IndustryService::FIELD_ID,
            ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID
        )->leftJoin(LeadProcessingReservedLead::TABLE, fn(JoinClause $joinClause) => $joinClause->on(
            LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID,
            LeadProcessingAged::TABLE . '.' . LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID)
            ->where(LeadProcessingReservedLead::TABLE . '.' . LeadProcessingReservedLead::FIELD_PROCESSOR_ID, $processor->id)
        )->join(
            Consumer::TABLE,
                Consumer::TABLE . '.' . Consumer::FIELD_ID,
            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_ID
        );
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function applyBudgetFilter(Builder $query): void
    {
        $query->whereExists(fn(QueryBuilder $query) => $query->select(DB::raw(1))
            ->from(AvailableBudget::TABLE)
            ->where(fn(QueryBuilder $query) => $query->where(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_BUDGET_AVAILABLE_DOLLARS, '>', 0)
                ->orWhere(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_BUDGET_AVAILABLE_VOLUME, '>', 0)
                ->orWhere(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT, '>', 0))
            ->whereColumn(
                AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_LOCATION_ID,
                Address::TABLE  . '.' . Address::FIELD_ZIP_CODE_LOCATION_ID
            )
            ->whereColumn(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_INDUSTRY_ID, IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID)
        );
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function applyNoLimitBudgetFilter(Builder $query): void
    {
        $query->whereExists(fn(QueryBuilder $query) => $query->select(DB::raw(1))
            ->from(AvailableBudget::TABLE)
            ->where(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT, '>', 0)
            ->whereColumn(
                AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_LOCATION_ID,
                Address::TABLE . '.' . Address::FIELD_ZIP_CODE_LOCATION_ID
            )
            ->whereColumn(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_INDUSTRY_ID, IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID)
        );
    }

    /**
     * @param Builder $query
     * @param int[] $utc
     *
     * @return void
     */
    protected function applyQueueTimezoneFilter(Builder $query, array $utc): void
    {
        $query->whereIn(DB::raw('ROUND(' . USZipCode::TABLE . '.'. USZipCode::FIELD_UTC . ')'), $utc);
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function applyCallingTimezoneFilter(Builder $query): void
    {
        $defaultUtc           = ProductProcessingService::FALLBACK_UTC_OFFSET;
        $defaultTimezone      = LeadProcessingCallingTimeZoneConfiguration::query()->where(LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, $defaultUtc)->first();
        $defaultTimezoneOpen  = $defaultTimezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR];
        $defaultTimezoneClose = $defaultTimezone[LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR];
        $timezoneOpenCol      = LeadProcessingCallingTimeZoneConfiguration::TABLE . '.' . LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR;
        $timezoneCloseCol     = LeadProcessingCallingTimeZoneConfiguration::TABLE . '.' . LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR;
        $zipcodeUtcCol        = USZipCode::TABLE . '.' . USZipCode::FIELD_UTC;
        $zipcodeDstCol        = USZipCode::TABLE . '.' . USZipCode::FIELD_DST;;

        $query->leftJoin(LeadProcessingCallingTimeZoneConfiguration::TABLE, fn(JoinClause $joinClause) => $joinClause->on(
            DB::raw('Round(' . USZipCode::TABLE . '.' . USZipCode::FIELD_UTC .')'),
            LeadProcessingCallingTimeZoneConfiguration::TABLE . '.' . LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET)
        )->whereRaw(
            "TIME_FORMAT(CONVERT_TZ(NOW(), '+00:00', CONCAT(IFNULL(ROUND($zipcodeUtcCol), $defaultUtc) + IF($zipcodeDstCol = 'Y', 1, 0), ':00')), '%k') >= IFNULL($timezoneOpenCol, $defaultTimezoneOpen)"
        )->whereRaw(
            "TIME_FORMAT(CONVERT_TZ(NOW(), '+00:00', CONCAT(IFNULL(ROUND($zipcodeUtcCol), $defaultUtc) + IF($zipcodeDstCol = 'Y', 1, 0), ':00')), '%k') < IFNULL($timezoneCloseCol, $defaultTimezoneClose)"
        );
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function orderBy(Builder $query): void
    {
        $query->orderByDesc(LeadProcessingAged::TABLE . '.' . LeadProcessingAged::FIELD_RECENCY_POINTS);
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function applySkippedLeadsFilter(Builder $query): void
    {
        $query->where(fn(Builder $query) => $query->whereNull(LeadProcessingAged::TABLE . '.' . LeadProcessingAged::FIELD_SKIPPED_AT)
            ->orWhereRaw('TIMESTAMPDIFF(DAY, ' . LeadProcessingAged::TABLE . '.' . LeadProcessingAged::FIELD_SKIPPED_AT . ', now()) > ' . self::SKIP_DELAY_DAYS)
        );
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function applyFullyAllocatedFilter(Builder $query): void
    {
        $service = app(AgedQueueService::class);
        $query->leftJoinSub($service->getAllocationSubQuery(now()->subDays(self::MAX_DAYS)), 'pa', fn(JoinClause $joinClause) => $joinClause->on(
            'pa.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
            ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID
        ))->where(
            fn(Builder $query) => $query->whereNull('pa.sold_count')
                ->orWhereRaw('pa.sold_count < ' . ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONTACT_REQUESTS)
        );
    }

    /**
     * filter out text, calls, voicemails, recent activity logs
     *
     * @param Builder $query
     * @return void
     */
    protected function applyRecentlyWorkedFilter(Builder $query): void
    {
        $query
            ->whereNotExists(fn(QueryBuilder $query) => $query->select(DB::raw(1))
                ->from(Call::TABLE)
                ->whereColumn(
                    Call::TABLE . '.' . Call::FIELD_FORMATTED_OTHER_NUMBER,
                    Consumer::TABLE . '.' . Consumer::FIELD_FORMATTED_PHONE)
                ->where(Call::TABLE . '.' . Call::CREATED_AT, '>=', now()->subHours(self::MAX_RECENTLY_CONTACTED_HOURS))
            )
            ->whereNotExists(fn(QueryBuilder $query) => $query->select(DB::raw(1))
                ->from(Text::TABLE)
                ->whereColumn(
                    Text::TABLE . '.' . Text::FIELD_OTHER_NUMBER,
                    Consumer::TABLE . '.' . Consumer::FIELD_FORMATTED_PHONE)
                ->where(Text::TABLE . '.' . Text::CREATED_AT, '>=', now()->subHours(self::MAX_RECENTLY_CONTACTED_HOURS))
            )
            ->whereNotExists(fn(QueryBuilder $query) => $query->select(DB::raw(1))
                ->from(Voicemail::TABLE)
                ->whereColumn(
                    Voicemail::TABLE . '.' . Voicemail::FIELD_FROM_NUMBER,
                    Consumer::TABLE . '.' . Consumer::FIELD_FORMATTED_PHONE)
                ->where(Voicemail::TABLE . '.' . Voicemail::CREATED_AT, '>=', now()->subHours(self::MAX_RECENTLY_CONTACTED_HOURS))
            )
        ;
    }
}
