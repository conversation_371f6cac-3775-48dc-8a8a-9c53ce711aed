<?php

namespace App\Http\Resources\Dashboard;

use App\Models\Odin\CompanyUser;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Transformers\Odin\CompanyUserDataTransformer;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;
use App\Models\User;

/**
 * @mixin CompanyUser
 */
class CompanyUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request)
    {
        $dataTransformer = app()->make(CompanyUserDataTransformer::class);
        $createdBy = User::find($this->created_by_id);
        $updatedBy = User::find($this->updated_by_id);

        return [
            'id'                                => $this->id,
            'name'                              => $this->completeName(),
            'first_name'                        => $this->first_name,
            'last_name'                         => $this->last_name,
            'title'                             => $this->title,
            'department'                        => $this->department,
            'email'                             => $this->email,
            'notes'                             => $this->notes,
            'status'                            => $this->status,
            'pinned'                            => $this->pinned,
            'date_registered'                   => $this->created_at?->toIso8601String(),
            'cell_phone'                        => $this->cell_phone,
            'office_phone'                      => $this->office_phone,
            'is_contact'                        => $this->is_contact,
            'legacy_id'                         => intval($this->legacy_id),
            'is_decision_maker'                 => $this->is_decision_maker,
            'can_receive_promotion'             => $this->can_receive_promotions,
            'total_calls_count'                 => $this['office_phone_calls_count'] + $this['cell_phone_calls_count'],
            'total_calls_over_one_minute_count' => $this->office_phone_calls_over_one_minute->count() + $this->cell_phone_calls_over_one_minute->count(),
            'latest_call_timestamp'             => $this->latest_call?->call_end?->getTimestamp(),
            'created_by_name'                   => $createdBy ? $createdBy->name : null,
            'created_by_email'                  => $createdBy ? $createdBy->email : null,
            'updated_by_name'                   => $updatedBy ? $updatedBy->name : null,
            'updated_by_email'                  => $updatedBy ? $updatedBy->email : null,
            'user_fields'                       => $dataTransformer->transform($this->resource),
            'must_reset_password_token'         => $this['must_reset_password_token'],
            'dnc_contact'                       => $this->dnc_contact,
            'import_source'                     => $this->import_source,
            'must_reset_password'               => $this->must_reset_password,
            'forced_password_reset_token'       => $this->must_reset_password ? app(DashboardJWTService::class)->getForcedPasswordResetToken($this->resource) : null,
        ];
    }

}
