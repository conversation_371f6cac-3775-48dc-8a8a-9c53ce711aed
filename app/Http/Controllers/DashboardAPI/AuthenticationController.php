<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Http\Requests\Dashboard\ForcedPasswordUpdateRequest;
use App\Http\Requests\Dashboard\LoginRequest;
use App\Http\Requests\Dashboard\LoginWithTokenRequest;
use App\Models\Odin\CompanyUser;
use App\Rules\StrongPassword;
use App\Services\CompanyUserService;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\DashboardLoginTokenService;
use Exception;
use Illuminate\Http\JsonResponse;

class AuthenticationController extends BaseDashboardApiController
{
    const string FIELD_TOKEN            = 'token';
    const string FIELD_EXPIRY           = 'expiry';
    const string FIELD_EMAIL            = 'email';
    const string FIELD_NEW_PASSWORD     = 'password';
    const string FIELD_CONFIRM_PASSWORD = 'password_confirmation';

    const string RESPONSE_STATUS         = 'status';
    const string RESPONSE_MESSAGE        = 'message';
    const string RESPONSE_IS_SHADOWER    = 'is_shadower';
    const string RESPONSE_MIGRATING      = 'migrating_user';
    const string RESPONSE_MIGRATE_STATUS = 'migrating_status';

    const string AUTH_MIGRATE_PREFIX  = '__MIGRATE__';
    const string AUTH_MIGRATE_USER    = 'SUCCESS';
    const string AUTH_MIGRATE_ERROR   = 'ERROR';

    public function login(LoginRequest $request): JsonResponse
    {
        $fields = $request->safe();

        try {
            $token = $this->authService->authenticate(
                $fields->offsetGet(LoginRequest::FIELD_EMAIL),
                $fields->offsetGet(LoginRequest::FIELD_PASSWORD)
            );

            if (str_starts_with($token, self::AUTH_MIGRATE_PREFIX))
                return $this->handleMigratingUserResponse($token);

        } catch (\Exception $e) {
            return $this->makeErrorResponse([$e->getMessage()]);
        }

        return $this->formatResponse([
            self::FIELD_TOKEN  => $token,
            self::FIELD_EXPIRY => $this->jwtService->decode($token)->getExpiresAt()
        ]);
    }

    public function loginWithToken(LoginWithTokenRequest $request, DashboardLoginTokenService $service): JsonResponse
    {
        $fields = $request->safe();

        try {
            $tokenModel = $service->burnToken($fields->offsetGet(LoginWithTokenRequest::FIELD_TOKEN));
            $user = $tokenModel?->companyUser;
            $shadower = $tokenModel?->shadower ?? null;

            if (!$user)
                throw new \Exception("Login token is invalid, please login with email and password.");

            $token = $this->authService->authenticateUser($user, $shadower);
        } catch (\Exception $e) {
            return $this->makeErrorResponse([$e->getMessage()]);
        }

        return $this->formatResponse([
            self::FIELD_TOKEN          => $token,
            self::FIELD_EXPIRY         => $this->jwtService->decode($token)->getExpiresAt(),
            self::RESPONSE_IS_SHADOWER => (bool) $tokenModel?->shadower_id,
        ]);
    }

    /**
     * Update a users password from a password reset request
     * Sends back a burn token to attempt auto-login if update successful
     *
     * @param CompanyUserService $companyUserService
     * @param DashboardLoginTokenService $tokenService
     * @return JsonResponse
     */
    public function updatePassword(CompanyUserService $companyUserService, DashboardLoginTokenService $tokenService): JsonResponse
    {
        $minimumPasswordLength = CompanyUser::AUTHENTICATION_MINIMUM_PASSWORD_LENGTH;
        $validated = $this->request->validate([
            self::FIELD_TOKEN            => ['required'],
            self::FIELD_EMAIL            => ['required', 'email'],
            self::FIELD_NEW_PASSWORD     => ['required', new StrongPassword],
            self::FIELD_CONFIRM_PASSWORD => ['required', 'same:' . self::FIELD_NEW_PASSWORD],
        ]);

        try {
            $user = $this->authService->getUserForPasswordReset($validated[self::FIELD_EMAIL], $validated[self::FIELD_TOKEN]);
            $isMigratingUser = $user->authentication_type === CompanyUser::AUTHENTICATION_TYPE_LEGACY;

            $passwordChanged = $companyUserService->updatePassword($user, $validated[self::FIELD_NEW_PASSWORD]);

            if (!$passwordChanged)
                throw new Exception("Failed to update password.");

            // Make sure a newly migrated CompanyUser is correctly set in Admin2.0
            if ($isMigratingUser) {
                $user->update([
                    CompanyUser::FIELD_CAN_LOG_IN => CompanyUser::USER_ALLOWED_TO_LOGIN,
                    CompanyUser::FIELD_IS_CONTACT => CompanyUser::USER_IS_NOT_CONTACT,
                    CompanyUser::FIELD_STATUS     => CompanyUser::STATUS_ACTIVE,
                ]);
            }

            $this->authService->cleanUpPasswordResets($validated[self::FIELD_EMAIL]);
            $newToken = $tokenService->generateToken($user);
            $user->update([
                CompanyUser::FIELD_MUST_RESET_PASSWORD => false
            ]);
        }
        catch (Exception $e) {
            return $this->formatResponse([
                self::RESPONSE_STATUS  => false,
                self::RESPONSE_MESSAGE => $e->getMessage()
            ]);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::FIELD_TOKEN     => $newToken,
        ]);
    }

    /**
     * @param DashboardAuthService $authService
     * @param CompanyUserService $companyUserService
     * @param ForcedPasswordUpdateRequest $request
     *
     * @return JsonResponse
     */
    public function forcedPasswordUpdate(DashboardAuthService $authService, CompanyUserService $companyUserService, ForcedPasswordUpdateRequest $request): JsonResponse
    {
        $companyUser = $authService->validateForcedResetPasswordToken(
            token: $request->validated(ForcedPasswordUpdateRequest::FIELD_TOKEN),
            email: $request->validated(ForcedPasswordUpdateRequest::FIELD_EMAIL)
        );

        $companyUserService->updatePassword(
            companyUser: $companyUser,
            rawPassword: $request->validated(ForcedPasswordUpdateRequest::FIELD_NEW_PASSWORD)
        );
        $companyUser->update([
            CompanyUser::FIELD_MUST_RESET_PASSWORD => false
        ]);

        return $this->formatResponse([
            self::RESPONSE_STATUS => true
        ]);
    }

    /**
     * Only send back a generic error on internal errors, or basic validation error
     * to prevent address scanning
     *
     * @param CompanyUserService $companyUserService
     * @return JsonResponse
     */
    public function resetPassword(CompanyUserService $companyUserService): JsonResponse
    {
        $email = $this->request->validate([
            'email' => 'required|email',
        ]);

        try {
            /** @var CompanyUser $user */
            $user = CompanyUser::query()
                ->where(CompanyUser::FIELD_EMAIL, $email)
                ->first();

            if ($user)
                $companyUserService->resetCompanyUserPassword($user);
        }
        catch(Exception $e) {
            logger()->error("Error attempting to reset CompanyUser password from Dashboard: ". $e->getMessage());

            return $this->formatResponse([
                self::RESPONSE_STATUS  => false,
                self::RESPONSE_MESSAGE => "There was an error contacting the server for a password reset. Please try again later."
            ]);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
        ]);
    }

    /**
     * Custom response for dashboard to handle users migrating from roofing/solar
     *
     * @param string $authResponse
     * @return JsonResponse
     */
    private function handleMigratingUserResponse(string $authResponse): JsonResponse
    {
        $match = [];
        preg_match("/__\w+__(.*)/", $authResponse, $match);
        $success = $match[1] === self::AUTH_MIGRATE_USER;

        return $this->formatResponse([
            self::RESPONSE_STATUS         => false,
            self::RESPONSE_MIGRATING      => true,
            self::RESPONSE_MIGRATE_STATUS => $success,
        ]);
    }
}
