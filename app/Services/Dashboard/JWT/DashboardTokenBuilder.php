<?php

namespace App\Services\Dashboard\JWT;

use App\Builders\DashboardShadowBuilder;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use Firebase\JWT\JWT;
use Illuminate\Support\Carbon;

class DashboardTokenBuilder extends DashboardShadowBuilder
{
    /**
     * @inheritDoc
     */
    public static function query(): self
    {
        return new DashboardTokenBuilder(Carbon::now()->timestamp);
    }

    /**
     * Returns the payload for the JWT.
     *
     * @return array
     */
    protected function getPayload(): array
    {
        $payload = [
            DashboardTokenModel::FIELD_USER_ID    => $this->userId,
            DashboardTokenModel::FIELD_ISSUED_AT  => $this->issuedAt,
            DashboardTokenModel::FIELD_EXPIRES_AT => \Carbon\Carbon::createFromTimestamp($this->issuedAt)->addSeconds($this->expiration)->timestamp,
            DashboardTokenModel::FIELD_DATA       => [],
        ];

        /** @var CompanyUser|null $user */
        $user = CompanyUser::query()->find($this->userId);

        if ($user) {
            $payload[DashboardTokenModel::FIELD_USER_NAME] = $user->first_name . ' ' . $user->last_name;
            $payload[DashboardTokenModel::FIELD_USER_EMAIL] = $user->email;
            $payload[DashboardTokenModel::FIELD_COMPANY_ID] = $user->company_id;

            if ($user->company) {
                $payload[DashboardTokenModel::FIELD_COMPANY_NAME] = $user->company->name;
            }
        }

        if ($this->shadowingUserId) {
            /** @var User|null $adminUser */
            $adminUser = User::query()->findOrFail($this->shadowingUserId);

            if ($adminUser) {
                $payload[DashboardTokenModel::FIELD_SHADOWER_ID] = $this->shadowingUserId;
                $payload[DashboardTokenModel::FIELD_SHADOWER_NAME] = $adminUser->name;
                $payload[DashboardTokenModel::FIELD_SHADOWER_EMAIL] = $adminUser->email;
            }
        }

        return $payload;
    }

    public function getTokenForCustomPayload(array $payload): string
    {
        return JWT::encode($payload, $this->signingKey, $this->algorithm);
    }
}
