<?php

namespace App\Services;

use App\Enums\CompanyUserVerificationMethod;
use App\Http\Controllers\CompanyUserController;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Notifications\CompanyUserPasswordReset;
use App\Repositories\Odin\CompanyUserRepository;
use App\Services\Legacy\APIConsumer;
use App\Services\Verification\CompanyUserVerificationService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class CompanyUserService
{
    const string PASSWORD_RESET_ORIGIN_FIXR = 'fixr';

    public function __construct
    (
        protected CompanyUserVerificationService $verificationService,
        protected CompanyUserRepository $repository
    ) {}

    /**
     * @param string $rawPassword
     * @return string
     */
    private function encryptPassword(string $rawPassword): string
    {
        return Hash::make($rawPassword);
    }

    /**
     * @param CompanyUser $companyUser
     * @param string $rawPassword
     * @return bool
     */
    public function updatePassword(CompanyUser $companyUser, string $rawPassword): bool
    {
        $encrypted = strlen($rawPassword) >= CompanyUser::AUTHENTICATION_MINIMUM_PASSWORD_LENGTH
            ? $this->encryptPassword($rawPassword)
            : null;
        if (!$encrypted) return false;
        $companyUser->password = $encrypted;
        $companyUser->authentication_type = CompanyUser::AUTHENTICATION_TYPE_ADMIN2;

        return $companyUser->save();
    }

    /**
     * Sends a password reset email to a CompanyUser
     * Disable $ignoreEmailValidation to send a validation email if the CompanyUser hasn't yet verified their address
     * @param CompanyUser $companyUser
     * @param ?bool $ignoreEmailValidation
     * @return array
     */
    public function resetCompanyUserPassword(CompanyUser $companyUser, ?bool $ignoreEmailValidation = true): array
    {
        if ($ignoreEmailValidation || $companyUser->{CompanyUser::FIELD_EMAIL_VERIFIED_AT}) {
            $success = $this->sendPasswordResetNotification($companyUser);
            return [
                CompanyUserController::RESPONSE_STATUS  => $success,
                CompanyUserController::RESPONSE_MESSAGE => $success
                    ? "A password reset email has been sent to {$companyUser->{CompanyUser::FIELD_EMAIL}}"
                    : "An error occurred sending the password reset email to {$companyUser->{CompanyUser::FIELD_EMAIL}}"
            ];
        }
        else {
            $this->verificationService->sendVerificationNotification($companyUser, CompanyUserVerificationMethod::EMAIL);
            return [
                CompanyUserController::RESPONSE_STATUS  => false,
                CompanyUserController::RESPONSE_MESSAGE => "This user has not verified their email. A verification email has been sent to {$companyUser->{CompanyUser::FIELD_EMAIL}}"
            ];
        }
    }

    /**
     * @param Company $company
     * @param array $companyUserData
     * @return ?CompanyUser
     * @throws BindingResolutionException
     */
    public function createCompanyContactSynchronously(Company $company, array $companyUserData): ?CompanyUser
    {
        $companyReference = $company->reference;

        $companyUserData[CompanyUser::FIELD_COMPANY_ID] = $company->id;
        $companyUserData[CompanyUser::FIELD_IS_CONTACT] = true;

        $companyUser = ($this->repository->createCompanyUserFromAttributes($companyUserData));
        $payload = [
            'company_reference' => $companyReference,
            'data' => [
                ...$companyUserData,
                'reference' => $companyUser->reference,
            ],
        ];

        $apiConsumer = app()->make(APIConsumer::class);
        $response = $apiConsumer->post("fixr-dashboard/$companyReference/company-contact", $payload)
            ?->json()['data'] ?? [];

        if ($response['legacy_id'] ?? false) {
            $companyUser->legacy_id = $response['legacy_id'];
            $companyUser->save();
            return $companyUser;
        }
        else {
            $companyUser->delete();
            return null;
        }
    }

    /**
     * @param CompanyUser $companyUser
     * @return bool
     */
    private function sendPasswordResetNotification(CompanyUser $companyUser): bool
    {
        $result = Password::broker(CompanyUser::TABLE)->sendResetLink(
            [ 'email' => $companyUser->{CompanyUser::FIELD_EMAIL} ],
            $this->sendEmailToUser(...),
        );

        return $result === Password::RESET_LINK_SENT;
    }

    /**
     * @param CompanyUser $companyUser
     * @param string $token
     * @return string
     */
    public function sendEmailToUser(CompanyUser $companyUser, string $token): string
    {
        try {
            $companyUser->notify($this->getMailableByOrigin($token, self::PASSWORD_RESET_ORIGIN_FIXR));
        }
        catch(Exception $e) {
            logger()->error("Error: could not send password reset email to CompanyUser ID $companyUser->id - " . $e->getMessage());

            return $e->getMessage();
        }

        return Password::RESET_LINK_SENT;
    }

    /**
     * Customise the domain and url for the password reset view depending on the service the CompanyUser is using
     *  currently only Fixr Dashboard
     *
     * @param string $token
     * @param string|null $origin
     * @return CompanyUserPasswordReset
     */
    private function getMailableByOrigin(string $token, ?string $origin = self::PASSWORD_RESET_ORIGIN_FIXR): CompanyUserPasswordReset
    {
        return match($origin) {
            self::PASSWORD_RESET_ORIGIN_FIXR => (new CompanyUserPasswordReset($token))
                ->setUrl(config('app.dashboard.fixr_url'))
                ->setMailProperties(
                    subject: 'Fixr Dashboard - Password Reset Request',
                    salutation: ' ',
                    fromEmail: "<EMAIL>",
                    fromName: "Fixr"
                ),
        };
    }

    /**
     * @param int $companyId
     * @return CompanyUser
     */
    public function findOrCreateSystemCompanyUser(
        int $companyId
    ): CompanyUser
    {
        $systemName = '__SYSTEM__';

        $sample = CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $companyId)
            ->where(CompanyUser::FIELD_FIRST_NAME, $systemName)
            ->first();

        if ($sample) {
            return $sample;
        }

        $data = [
            CompanyUser::FIELD_COMPANY_ID                   => $companyId,
            CompanyUser::FIELD_FIRST_NAME                   => $systemName,
            CompanyUser::FIELD_CAN_LOG_IN                   => false,
            CompanyUser::FIELD_STATUS                       => 0,
            CompanyUser::FIELD_REFERENCE                    => Str::uuid()->toString(),
            CompanyUser::FIELD_IS_CONTACT                   => 0,
            CompanyUser::FIELD_IS_DECISION_MAKER            => 0,
            CompanyUser::FIELD_CAN_RECEIVE_PROMOTIONS       => 0,
            CompanyUser::FIELD_UNSUBSCRIBED_FROM_PROMOTIONS => 1,
            CompanyUser::FIELD_MUST_RESET_PASSWORD          => false,
        ];

        return $this->repository->createCompanyUser(
            companyId: $companyId,
            data     : $data
        );
    }
}
