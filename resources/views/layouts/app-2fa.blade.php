<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap">

        <!-- Styles -->
        <link rel="stylesheet" href="{{ asset('/css/app.css') }}">
    </head>
    <body class="font-sans antialiased">
        <div id="admin-app">
            <div class="min-h-screen" :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <header class="relative z-50">
                    @include('layouts.navigation-2fa')
                </header>
                <!-- Page Content -->
                <main>
                    <div v-if="!hideMainContent">
                        {{ $slot }}
                    </div>
                </main>
            </div>
        </div>
        <script src="{{ asset('/js/app.js') }}"></script>
        <script src="https://cdn.tiny.cloud/1/{{ config('services.tiny_mce.api_key') }}/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
        <script async defer
                src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.maps.static_maps_key') }}">
        </script>
    </body>
</html>
