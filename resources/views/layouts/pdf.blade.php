<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <style>
            * {
                font-family: -apple-system, blinkmacsystemfont, "Segoe UI", roboto, oxygen, ubuntu, cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            }

            #pdf-app {
                margin: 0 !important;
                padding: 50px !important;
                padding-top: 0 !important;
                box-sizing: border-box;
            }

            .page-break-inside-avoid {
                page-break-inside: avoid;
            }

            .page-break-after-always {
                page-break-after: always;
            }

            .page-break-before-always {
                page-break-before: always;
            }

            .bottom-of-page {
                position: absolute;
                bottom: 0;
            }
        </style>

        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body>
    <div id="pdf-app">
        {{ $slot }}
    </div>
    </body>
</html>
