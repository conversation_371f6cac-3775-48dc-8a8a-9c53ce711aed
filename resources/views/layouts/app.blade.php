@props(['title'])

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}@isset($title) - {{ $title }}@endisset</title>

        <!-- Fonts -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap">

        <!-- Styles -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        @stack('head_scripts')
    </head>
    <body class="font-sans antialiased">
        <div id="admin-app">
            <div class="min-h-screen" :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <header class="relative z-50">
                    @if(app()->environment('staging'))
                        @include('partials.staging')
                    @endif
                    @include('partials.impersonate')
                    @include('layouts.navigation')
                    @include('components.phone-service')
                </header>
                <!-- Page Content -->
                <main>
                    <div v-if="!hideMainContent">
                        {{ $slot }}
                    </div>

                    <div>
                        <global-company-wrapper :dark-mode="darkMode" />
                    </div>
                </main>
            </div>
        </div>
        <script src="https://cdn.tiny.cloud/1/{{ config('services.tiny_mce.api_key') }}/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
        {{--Do not load google maps if loading calculator builder script to avoid loading google maps multiple times--}}
        @if(isset($loadCbScript) && $loadCbScript === true)
            <script src="{{ config('services.flow_builder.url') }}"></script>
        @elseif(isset($loadFlowClientScript) && $loadFlowClientScript === true)
            <script src="{{ config('services.flow_client.url') }}"></script>
        @else
            <script async defer
                    src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.maps.static_maps_key') }}">
            </script>
        @endif
    </body>
</html>
