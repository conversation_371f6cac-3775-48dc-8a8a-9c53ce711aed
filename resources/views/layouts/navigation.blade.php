<div class="app-header w-full flex justify-between items-center border-b py-1 px-5 fixed text-caption font-medium z-50 h-16" :class="{'bg-dark-module border-dark-border': darkMode, 'bg-light-module border-light-border': !darkMode}">
    {{--  List of Links with Permission Wrappers  --}}
    <div class="flex items-center">
        <div class="w-10 h-10 inline-flex items-center justify-center ml-4 mr-5 cursor-pointer text-blue-550 hover:text-blue-500" @click="toggleNavigationMenu">
            <svg class="w-full fill-current" width="29" height="17" viewBox="0 0 29 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 1.40972C0 0.631154 0.631154 0 1.40972 0H21.5903C22.3688 0 23 0.631154 23 1.40972C23 2.18829 22.3688 2.81944 21.5903 2.81944H1.40972C0.631153 2.81944 0 2.18829 0 1.40972Z"/>
                <path d="M0 8.45833C0 7.67977 0.631154 7.04861 1.40972 7.04861H27.5903C28.3688 7.04861 29 7.67977 29 8.45833C29 9.2369 28.3688 9.86806 27.5903 9.86806H1.40972C0.631154 9.86806 0 9.2369 0 8.45833Z"/>
                <path d="M0 15.5069C0 14.7284 0.631154 14.0972 1.40972 14.0972H13.5903C14.3688 14.0972 15 14.7284 15 15.5069C15 16.2855 14.3688 16.9167 13.5903 16.9167H1.40972C0.631155 16.9167 0 16.2855 0 15.5069Z"/>
            </svg>
        </div>
        <div @click="toggleNavigationMenu" :class="{'opacity-0 h-0 w-0' : !navShowing, 'opacity-75 h-full w-full' : navShowing, 'bg-light-background' : !darkMode, 'bg-dark-background' : darkMode}" class="fixed inset-0 flex items-center transition-opacity duration-400 justify-center z-40"></div>
        <div :class="{'left-0 opacity-100' : navShowing, '-left-96 opacity-0' : !navShowing, 'bg-light-module border-r border-grey-100': !darkMode, 'bg-dark-module border-r border-dark-border': darkMode}" class="opacity-0 absolute top-0 shadow-module transition-all duration-300 overflow-y-auto h-screen w-80 z-50">
            <div class="w-full">
                <div class="pt-3 pb-5">
                    <div class="flex-grow flex flex-col">
                        <div class="flex justify-between items-center px-5 mb-4">
                            <button class="dark-mode-toggle flex w-20 h-8 border rounded-lg relative cursor-pointer transition-all duration-100 items-center" :class="[darkMode ? 'border-dark-border bg-dark-background' : 'border-light-border bg-light-background']" @click="toggleDarkMode">
                                <svg :class="[darkMode ? 'text-blue-550' : 'text-slate-500']" class="absolute z-20 right-2 w-6 dark-mode-svg-icon fill-current transition duration-200" width="16" height="13" viewBox="0 0 16 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.56326 6.43695C5.73743 5.61077 5.17502 4.55833 4.9471 3.41262C4.71918 2.26691 4.83598 1.07935 5.28273 0C4.00895 0.250764 2.83891 0.875794 1.92226 1.79515C-0.640754 4.35825 -0.640754 8.51434 1.92226 11.0774C4.48593 13.6412 8.64123 13.6405 11.2049 11.0774C12.124 10.1608 12.749 8.99105 13 7.71751C11.9207 8.16419 10.7332 8.28095 9.58751 8.05302C8.44185 7.8251 7.38945 7.26273 6.56326 6.43695Z"/>
                                    <path d="M15.9868 2.795C15.9711 2.74873 15.9421 2.70806 15.9036 2.67803C15.865 2.64801 15.8185 2.62993 15.7698 2.62603L14.3445 2.51279L13.7278 1.14772C13.7081 1.10376 13.6762 1.06641 13.6358 1.0402C13.5954 1.01399 13.5483 1.00003 13.5001 1C13.4519 0.999974 13.4048 1.01388 13.3644 1.04005C13.3239 1.06622 13.292 1.10353 13.2723 1.14747L12.6555 2.51279L11.2303 2.62603C11.1824 2.62982 11.1366 2.64733 11.0984 2.67645C11.0602 2.70558 11.0312 2.74509 11.0149 2.79026C10.9986 2.83543 10.9956 2.88435 11.0063 2.93116C11.0171 2.97797 11.0411 3.0207 11.0755 3.05422L12.1288 4.08083L11.7563 5.69362C11.745 5.74244 11.7486 5.79353 11.7667 5.84026C11.7848 5.88699 11.8165 5.92721 11.8577 5.95569C11.8989 5.98418 11.9478 5.99961 11.9979 5.99999C12.048 6.00037 12.0971 5.98569 12.1388 5.95784L13.5 5.05046L14.8613 5.95784C14.9039 5.9861 14.9541 6.00066 15.0052 5.99954C15.0563 5.99842 15.1058 5.98168 15.1471 5.95157C15.1884 5.92146 15.2195 5.87942 15.2362 5.83112C15.2529 5.78281 15.2544 5.73056 15.2405 5.68137L14.7833 4.08158L15.9173 3.06122C15.9915 2.99423 16.0188 2.88974 15.9868 2.795Z"/>
                                </svg>
                                <svg :class="[darkMode ? 'text-slate-500' : 'text-blue-550']" class="absolute z-20 left-2 w-6 light-mode-svg-icon fill-current transition duration-200" width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9 0C9.29837 0 9.58452 0.111942 9.79549 0.311199C10.0065 0.510456 10.125 0.780707 10.125 1.0625V2.125C10.125 2.40679 10.0065 2.67704 9.79549 2.8763C9.58452 3.07556 9.29837 3.1875 9 3.1875C8.70163 3.1875 8.41548 3.07556 8.20451 2.8763C7.99353 2.67704 7.875 2.40679 7.875 2.125V1.0625C7.875 0.780707 7.99353 0.510456 8.20451 0.311199C8.41548 0.111942 8.70163 0 9 0V0ZM13.5 8.5C13.5 9.62717 13.0259 10.7082 12.182 11.5052C11.3381 12.3022 10.1935 12.75 9 12.75C7.80653 12.75 6.66193 12.3022 5.81802 11.5052C4.97411 10.7082 4.5 9.62717 4.5 8.5C4.5 7.37283 4.97411 6.29183 5.81802 5.4948C6.66193 4.69777 7.80653 4.25 9 4.25C10.1935 4.25 11.3381 4.69777 12.182 5.4948C13.0259 6.29183 13.5 7.37283 13.5 8.5V8.5ZM12.978 13.7594L13.7734 14.5106C13.9856 14.7041 14.2697 14.8112 14.5647 14.8088C14.8597 14.8064 15.1418 14.6946 15.3504 14.4976C15.559 14.3006 15.6773 14.0341 15.6799 13.7556C15.6824 13.477 15.5691 13.2086 15.3641 13.0082L14.5687 12.257C14.3566 12.0635 14.0724 11.9564 13.7774 11.9588C13.4825 11.9612 13.2003 12.0729 12.9917 12.2699C12.7831 12.4669 12.6648 12.7334 12.6622 13.012C12.6597 13.2906 12.7731 13.559 12.978 13.7594V13.7594ZM15.363 2.48944C15.5739 2.68869 15.6924 2.95889 15.6924 3.24063C15.6924 3.52236 15.5739 3.79256 15.363 3.99181L14.5687 4.743C14.465 4.84448 14.3408 4.92542 14.2036 4.98111C14.0663 5.03679 13.9187 5.0661 13.7693 5.06733C13.6199 5.06855 13.4718 5.04167 13.3336 4.98825C13.1953 4.93482 13.0697 4.85593 12.9641 4.75617C12.8584 4.65641 12.7749 4.53778 12.7183 4.4072C12.6618 4.27662 12.6333 4.13671 12.6346 3.99564C12.6359 3.85456 12.6669 3.71514 12.7259 3.58551C12.7848 3.45588 12.8706 3.33864 12.978 3.24063L13.7734 2.48944C13.9843 2.29025 14.2704 2.17835 14.5687 2.17835C14.8671 2.17835 15.1532 2.29025 15.3641 2.48944H15.363ZM16.875 9.5625C17.1734 9.5625 17.4595 9.45056 17.6705 9.2513C17.8815 9.05204 18 8.78179 18 8.5C18 8.21821 17.8815 7.94796 17.6705 7.7487C17.4595 7.54944 17.1734 7.4375 16.875 7.4375H15.75C15.4516 7.4375 15.1655 7.54944 14.9545 7.7487C14.7435 7.94796 14.625 8.21821 14.625 8.5C14.625 8.78179 14.7435 9.05204 14.9545 9.2513C15.1655 9.45056 15.4516 9.5625 15.75 9.5625H16.875ZM9 13.8125C9.29837 13.8125 9.58452 13.9244 9.79549 14.1237C10.0065 14.323 10.125 14.5932 10.125 14.875V15.9375C10.125 16.2193 10.0065 16.4895 9.79549 16.6888C9.58452 16.8881 9.29837 17 9 17C8.70163 17 8.41548 16.8881 8.20451 16.6888C7.99353 16.4895 7.875 16.2193 7.875 15.9375V14.875C7.875 14.5932 7.99353 14.323 8.20451 14.1237C8.41548 13.9244 8.70163 13.8125 9 13.8125V13.8125ZM3.43125 4.743C3.5357 4.84172 3.65972 4.92004 3.79621 4.97349C3.93271 5.02694 4.07902 5.05448 4.22679 5.05453C4.37456 5.05458 4.52089 5.02714 4.65743 4.97378C4.79397 4.92042 4.91804 4.84218 5.02256 4.74353C5.12709 4.64488 5.21001 4.52776 5.26661 4.39884C5.32321 4.26993 5.35236 4.13175 5.35242 3.99219C5.35247 3.85263 5.32341 3.71443 5.26691 3.58548C5.21042 3.45652 5.12758 3.33934 5.02313 3.24063L4.22662 2.48944C4.01445 2.29589 3.73027 2.1888 3.4353 2.19122C3.14033 2.19364 2.85816 2.30538 2.64958 2.50238C2.44099 2.69938 2.32268 2.96586 2.32012 3.24445C2.31755 3.52303 2.43095 3.79142 2.63587 3.99181L3.43125 4.743V4.743ZM5.022 13.7594L4.22662 14.5106C4.01445 14.7041 3.73027 14.8112 3.4353 14.8088C3.14033 14.8064 2.85816 14.6946 2.64958 14.4976C2.44099 14.3006 2.32268 14.0341 2.32012 13.7556C2.31755 13.477 2.43095 13.2086 2.63587 13.0082L3.43125 12.257C3.64343 12.0635 3.92761 11.9564 4.22258 11.9588C4.51755 11.9612 4.79971 12.0729 5.0083 12.2699C5.21688 12.4669 5.3352 12.7334 5.33776 13.012C5.34032 13.2906 5.22693 13.559 5.022 13.7594V13.7594ZM2.25 9.5625C2.54837 9.5625 2.83452 9.45056 3.04549 9.2513C3.25647 9.05204 3.375 8.78179 3.375 8.5C3.375 8.21821 3.25647 7.94796 3.04549 7.7487C2.83452 7.54944 2.54837 7.4375 2.25 7.4375H1.125C0.826631 7.4375 0.540483 7.54944 0.329505 7.7487C0.118526 7.94796 0 8.21821 0 8.5C0 8.78179 0.118526 9.05204 0.329505 9.2513C0.540483 9.45056 0.826631 9.5625 1.125 9.5625H2.25Z"/>
                                </svg>
                                <span class="w-9 h-6 border-1 border-transparent top-1/2 -translate-y-1/2 rounded-md duration-200 absolute z-10 transition-all" :class="[darkMode ? 'bg-dark-module left-10' : 'left-0.5 bg-light-module']"></span>
                            </button>
                            <div @click="toggleNavigationMenu" class="w-10 h-10 cursor-pointer text-blue-550 hover:text-blue-500 inline-flex items-center justify-center">
                                <svg class="w-8 fill-current transition-opacity duration-200" :class="{'opacity-100' : navShowing, 'opacity-0' : !navShowing}" width="29" height="17" viewBox="0 0 29 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0 1.40972C0 0.631154 0.631154 0 1.40972 0H21.5903C22.3688 0 23 0.631154 23 1.40972C23 2.18829 22.3688 2.81944 21.5903 2.81944H1.40972C0.631153 2.81944 0 2.18829 0 1.40972Z"/>
                                    <path d="M0 8.45833C0 7.67977 0.631154 7.04861 1.40972 7.04861H27.5903C28.3688 7.04861 29 7.67977 29 8.45833C29 9.2369 28.3688 9.86806 27.5903 9.86806H1.40972C0.631154 9.86806 0 9.2369 0 8.45833Z"/>
                                    <path d="M0 15.5069C0 14.7284 0.631154 14.0972 1.40972 14.0972H13.5903C14.3688 14.0972 15 14.7284 15 15.5069C15 16.2855 14.3688 16.9167 13.5903 16.9167H1.40972C0.631155 16.9167 0 16.2855 0 15.5069Z"/>
                                </svg>
                            </div>
                        </div>
                        <nav class="flex-1" aria-label="Sidebar">
                            @include('components.nav-links')
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        <a href="/" class="block">
            <app-logo :dark-mode="darkMode"/>
        </a>
    </div>
    <div>
        <global-command-search @close-global-search="toggleGlobalCommandSearch" v-if="globalCommandSearchShowing" :dark-mode="darkMode"></global-command-search>
    </div>
    <div v-cloak>
        <div class="flex items-center gap-1 mr-5">
            <div @click="toggleGlobalCommandSearch" class="transition duration-200 w-10 h-10 rounded-full inline-flex justify-center items-center cursor-pointer"
            :class="[darkMode ? 'bg-dark-module text-slate-400 border-primary-500 hover:border-blue-400 hover:bg-dark-background' : 'bg-light-module text-slate-600 border-light-border hover:border-primary-500 hover:bg-cyan-125']">
                <svg  class="mx-auto h-4 w-4 fill-current" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.24615 2.21538C5.91191 2.21538 4.63232 2.74541 3.68886 3.68886C2.74541 4.63232 2.21538 5.91191 2.21538 7.24615C2.21538 7.9068 2.34551 8.56099 2.59833 9.17135C2.85115 9.78171 3.22171 10.3363 3.68886 10.8034C4.15601 11.2706 4.7106 11.6412 5.32096 11.894C5.93132 12.1468 6.5855 12.2769 7.24615 12.2769C7.90681 12.2769 8.56099 12.1468 9.17135 11.894C9.78171 11.6412 10.3363 11.2706 10.8034 10.8034C11.2706 10.3363 11.6412 9.78171 11.894 9.17135C12.1468 8.56099 12.2769 7.9068 12.2769 7.24615C12.2769 5.91191 11.7469 4.63232 10.8034 3.68886C9.85999 2.74541 8.5804 2.21538 7.24615 2.21538ZM2.12235 2.12235C3.48127 0.763432 5.32435 0 7.24615 0C9.16795 0 11.011 0.763432 12.37 2.12235C13.7289 3.48127 14.4923 5.32435 14.4923 7.24615C14.4923 8.19773 14.3049 9.13999 13.9407 10.0191C13.7188 10.5549 13.434 11.0607 13.093 11.5265L17.6756 16.1091C18.1081 16.5416 18.1081 17.243 17.6756 17.6756C17.243 18.1081 16.5416 18.1081 16.1091 17.6756L11.5265 13.093C11.0607 13.434 10.5549 13.7188 10.0191 13.9407C9.13999 14.3049 8.19773 14.4923 7.24615 14.4923C6.29458 14.4923 5.35232 14.3049 4.47317 13.9407C3.59403 13.5766 2.79522 13.0428 2.12235 12.37C1.44948 11.6971 0.915734 10.8983 0.551581 10.0191C0.187427 9.13999 0 8.19773 0 7.24615C0 5.32435 0.763432 3.48127 2.12235 2.12235Z"/>
                </svg>
            </div>
            <a href="https://us-west-2b.online.tableau.com/" target="_blank" class="transition duration-200 w-10 h-10 rounded-full inline-flex justify-center items-center cursor-pointer" :class="[darkMode ? 'bg-dark-module text-slate-400 border-primary-500 hover:border-blue-400 hover:bg-dark-background' : 'bg-light-module text-slate-600 border-light-border hover:border-primary-500 hover:bg-cyan-125']">
                <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0 2.5V16.5C0 17.603 0.897 18.5 2 18.5H16C17.103 18.5 18 17.603 18 16.5V2.5C18 1.397 17.103 0.5 16 0.5H2C0.897 0.5 0 1.397 0 2.5ZM16.001 16.5H2V2.5H16L16.001 16.5Z" fill="currentColor"/>
                    <path d="M8 4.5H10V14.5H8V4.5ZM12 7.5H14V14.5H12V7.5ZM4 9.5H6V14.5H4V9.5Z" fill="currentColor"/>
                </svg>
            </a>
            <div>
                <communications
                    api-driver="{{ config('services.lead_processing.api_driver') }}"
                    :dark-mode="darkMode"
                    communication-driver="{{ config('services.communication.driver') }}"
                    pusher-app-key="<?php echo e(config('broadcasting.connections.pusher.key')); ?>"
                    pusher-app-cluster="<?php echo e(config('broadcasting.connections.pusher.options.cluster')); ?>"
                    dialer-is-attached-to-parent
                    :active-menu="activeMenu"
                    @update-active-menu="toggleUserMenu"

                    @if(\Illuminate\Support\Facades\Auth::user() !== null)
                        user-id="{{ \Illuminate\Support\Facades\Auth::id() }}"
                    @endif
                />
            </div>
            <div>
                <lead-processing-notifications
                    api-driver="{{ config('services.lead_processing.api_driver') }}"
                    :dark-mode="darkMode"
                    api-driver="{{ config('services.lead_processing.api_driver') }}"
                    pusher-app-key="<?php echo e(config('broadcasting.connections.pusher.key')); ?>"
                    pusher-app-cluster="<?php echo e(config('broadcasting.connections.pusher.options.cluster')); ?>"

                    @if(\Illuminate\Support\Facades\Auth::user() !== null)
                        user-id="{{ \Illuminate\Support\Facades\Auth::id() }}"
                    @endif
                />
            </div>
            <div class="relative">
                <div @click="toggleUserMenu('profile')" class="relative transition duration-200 w-10 h-10 rounded-full inline-flex justify-center items-center cursor-pointer" :class="[activeMenu === 'profile' ? 'bg-primary-500 text-white hover:bg-blue-500 border-transparent' : (darkMode ? 'bg-dark-module text-slate-400 border-primary-500 hover:border-blue-400 hover:bg-dark-background' : 'bg-light-module text-slate-600 border-light-border hover:border-primary-500 hover:bg-cyan-125')]">
                    <svg class="fill-current w-5" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11 0C5.0369 0 0 5.0369 0 11C0 16.9631 5.0369 22 11 22C16.9631 22 22 16.9631 22 11C22 5.0369 16.9631 0 11 0ZM11 5.5C12.8997 5.5 14.3 6.8992 14.3 8.8C14.3 10.7008 12.8997 12.1 11 12.1C9.1014 12.1 7.7 10.7008 7.7 8.8C7.7 6.8992 9.1014 5.5 11 5.5ZM5.3834 16.2492C6.3701 14.7972 8.0157 13.8292 9.9 13.8292H12.1C13.9854 13.8292 15.6299 14.7972 16.6166 16.2492C15.2108 17.754 13.2165 18.7 11 18.7C8.7835 18.7 6.7892 17.754 5.3834 16.2492Z"/>
                    </svg>
                    <span class="absolute right-2 bottom-2 w-2 h-2 border rounded-full" :class="[darkMode ? 'bg-emerald-400 border-dark-border' : 'bg-emerald-500 border-light-module']">
                    </span>
                </div>
                <div v-if="activeMenu === 'profile'" class="opacity-0 shadow-module px-3 py-4 rounded-lg w-72 absolute border top-10 right-0"  :class="{'opacity-100' : activeMenu === 'profile', 'bg-light-module border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                    <div class="mx-4">
                        <p class="pb-2 text-right" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">{{Auth::user()->name}}</p>
                        <a href="/edit-profile"><p class="pb-2 text-right cursor-pointer" :class="{'text-grey-400 hover:text-grey-600': !darkMode, 'text-blue-400 hover:text-blue-550': darkMode}">Edit Profile</p></a>
                        @if(Auth::user()->verified_2fa == false)
                            <a href="/2fa/setup"><p class="text-right cursor-pointer mb-2" :class="{'text-grey-400 hover:text-grey-600': !darkMode, 'text-blue-400 hover:text-blue-550': darkMode}">2FA Setup</p></a>
                        @endif
                        <a href="/user-settings"><p class="text-right cursor-pointer" :class="{'text-grey-400 hover:text-grey-600': !darkMode, 'text-blue-400 hover:text-blue-550': darkMode}">User Settings</p></a>
                        <div class="flex justify-end pt-3 border-t mt-3" :class="{'border-grey-100': !darkMode, 'border-dark-border': darkMode}">
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="transition duration-200 text-xs font-semibold focus:outline-none py-1 rounded px-5"
                                        :class="{'bg-grey-250 hover:bg-light-background text-white': !darkMode, 'bg-blue-500 hover:bg-primary-500 text-white': darkMode}">
                                    {{ __('Log Out') }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<div class="h-16 w-full"></div>
