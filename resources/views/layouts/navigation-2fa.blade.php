<div class="app-header w-full flex justify-between items-center border-b py-1 px-5 fixed text-caption font-medium z-50 h-16" :class="{'bg-dark-module border-dark-border': darkMode, 'bg-light-module border-light-border': !darkMode}">
    {{--  List of Links with Permission Wrappers  --}}
    <div class="flex items-center">
        <a href="/" class="block">
            <app-logo :dark-mode="darkMode"/>
        </a>
    </div>
    <div>
        <global-command-search @close-global-search="toggleGlobalCommandSearch" v-if="globalCommandSearchShowing" :dark-mode="darkMode"></global-command-search>
    </div>
    <div>
        <div class="flex items-center mr-5">

            <div class="relative">
                <button @click="toggleUserMenu('profile')" class="inline-flex items-center cursor-pointer px-3 rounded transition duration-200 text-sm font-semibold" :class="{'text-grey-100 hover:text-blue-550': darkMode, 'text-grey-800 hover:text-blue-500': !darkMode}">
                    <span class="bg-green-400 w-2 h-2 rounded-full mr-2">
                    </span>

                    {{ Auth::user()->name }}

                    <svg class="ml-2 w-2.5 fill-current" width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7071 0.292893C12.0976 0.683418 12.0976 1.31658 11.7071 1.70711L6.70711 6.70711C6.31658 7.09763 5.68342 7.09763 5.29289 6.70711L0.292893 1.70711C-0.0976315 1.31658 -0.0976315 0.683417 0.292893 0.292893C0.683417 -0.0976316 1.31658 -0.0976316 1.70711 0.292893L6 4.58579L10.2929 0.292893C10.6834 -0.0976312 11.3166 -0.0976311 11.7071 0.292893Z"/></svg>
                </button>
                <div v-if="activeMenu === 'profile'" class="opacity-0 shadow-module px-3 py-4 rounded-lg w-64 absolute mt-7 border top-0 right-0"  :class="{'opacity-100' : userMenu, 'bg-light-module border-grey-100': !darkMode, 'bg-dark-background border-primary-500': darkMode}">
                    <div class="mx-4">
                        <div class="flex justify-end" :class="{'border-grey-100': !darkMode, 'border-dark-border': darkMode}">
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="transition duration-200 text-xs font-semibold focus:outline-none py-1 rounded px-5"
                                        :class="{'bg-grey-250 hover:bg-light-background text-white': !darkMode, 'bg-blue-500 hover:bg-primary-500 text-white': darkMode}">
                                    {{ __('Log Out') }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<div class="h-16 w-full"></div>
