<x-app-layout title="Reports">
    <div class="main-layout font-body">
        <div class="w-full">
            <div class="w-full flex-auto pt-3 relative"
                 :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                <div class="px-10" :class="[darkMode ? 'text-white' : 'text-slate-900']">
                    <h3 class="text-xl font-medium leading-none mr-5 py-6">Reports</h3>
                    <div>
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <a class="inline-block transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5" href="{{ url('/reports/historical-available-budgets') }}">Historical Available Budgets</a><br>
                            </div>
                            <div>
                                <a class="inline-block transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5" href="{{ url('/reports/estimated-revenue-per-lead') }}">Estimated Revenue Per Lead</a>
                            </div>
                            <div>
                                <a class="inline-block transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5" href="{{ url('/reports/profitability-simulator') }}">Profitability Simulator</a>
                            </div>
                            <div>
                                <a class="inline-block transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5" href="{{ url('/reports/leads-report') }}">Leads Report</a>
                            </div>
                            <div>
                                <a class="inline-block transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5" href="{{ url('/reports/county-coverage-report') }}">County Coverage Report</a>
                            </div>
                            <div>
                                <a class="inline-block transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5" href="{{ url('/reports/revenue-streams') }}">Revenue Streams Report</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
