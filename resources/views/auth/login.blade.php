<x-guest-layout>
    <div class="grid md:grid-cols-2 min-h-screen bg-dark-background w-full items-center">
        <x-auth-card>
            <x-slot name="logo">
                <a href="/">
                    <x-application-logo-dark class="w-56 h-20 fill-current text-gray-500" />
                </a>
            </x-slot>

            <!-- Session Status -->
            <x-auth-session-status class="mb-4" :status="session('status')" />

            <!-- Validation Errors -->
            <x-auth-validation-errors class="mb-4" :errors="$errors" />

            <form class="mb-4" method="POST" action="{{ route('login') }}">
                @csrf

                <!-- Email Address -->
                <div>
                    <x-label class="text-grey-200" for="email" :value="__('Email')" />

                    <input id="email" class="block text-white rounded-md mt-1 w-full border border-blue-400 bg-blue-800 bg-opacity-25 focus:outline-none" type="email" name="email" :value="old('email')" required autofocus />
                </div>

                <!-- Password -->
                <div class="mt-4">
                    <x-label class="text-grey-200" for="password" :value="__('Password')" />

                    <input id="password" class="block text-white rounded-md mt-1 w-full border border-blue-400 bg-blue-800 bg-opacity-25 focus:outline-none"
                             type="password"
                             name="password"
                             required autocomplete="current-password" />
                </div>

                <!-- Remember Me -->
                <div class="block mt-6">
                    <label for="remember_me" class="inline-flex items-center">
                        <input id="remember_me" type="checkbox" class="cursor-pointer rounded border border-blue-400 bg-blue-800 bg-opacity-25 shadow-sm focus:border-blue-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" name="remember">
                        <span class="ml-2 text-sm text-blue-200">{{ __('Remember me') }}</span>
                    </label>
                </div>

                <div class="flex items-center justify-between mt-8">
                    @if (Route::has('password.request'))
                        <a class="underline text-sm text-blue-200 hover:text-blue-550" href="{{ route('password.request') }}">
                            {{ __('Forgot your password?') }}
                        </a>
                    @endif

                    <button class="bg-primary-500 text-white rounded focus:outline-none px-5 py-1 font-medium hover:bg-blue-400 transition duration-200">
                        {{ __('Login') }}
                    </button>
                </div>
            </form>
        </x-auth-card>
        <div class="hidden md:block w-full h-full bg-cover relative" style="background-image: url('/images/SR_Panels_Login.png')">
            <div class="absolute w-full h-full" style="background: linear-gradient(180deg, rgba(0, 0, 0, 0.58) 0%, rgba(0, 133, 255, 0.58) 100%);">

            </div>
        </div>
    </div>
</x-guest-layout>
