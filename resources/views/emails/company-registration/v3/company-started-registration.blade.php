@php
    use App\Models\Prospects\NewBuyerProspect;

    /**
    * @var NewBuyerProspect $newBuyerProspect
    */
@endphp

@component('vendor.mail.html.message')
    @component('vendor.mail.html.subcopy')
        {{ $newBuyerProspect->getSourceDataByKey(NewBuyerProspect::SOURCE_KEY_FIRST_NAME) }} {{ $newBuyerProspect->getSourceDataByKey(NewBuyerProspect::SOURCE_KEY_LAST_NAME) }} from {{ $newBuyerProspect->company_name }} has begun registration.
        <br>
        Phone: {{ $newBuyerProspect->company_phone }}
        <br>
        Email: {{ $newBuyerProspect->getSourceDataByKey(NewBuyerProspect::SOURCE_KEY_EMAIL) }}
    @endcomponent
    @component('vendor.mail.html.subcopy')
        Thanks, <br>
        {{ config('app.name') }}
    @endcomponent
@endcomponent



