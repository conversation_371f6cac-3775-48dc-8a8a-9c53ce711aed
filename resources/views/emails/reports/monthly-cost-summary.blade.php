@component('mail::message')
# Month to Date Cost Summary - {{ now()->format('F Y') }}

Hello {{ $contact->first_name }} {{ $contact->last_name }},

Please find below the monthly cost summary for **{{ $company->name }}** from {{ now()->startOfMonth()->format('M j') }} to {{ now()->format('M j, Y') }}.

## Lead Costs by Campaign

@component('mail::table')
| Campaign Name | Total Cost | Lead Count |
|:--------------|:-----------|:-----------|
@foreach($costSummaryData as $item)
| {{ $item['campaign_name'] }} | ${{ $item['total_cost'] }} | {{ $item['lead_count'] }} |
@endforeach
| **TOTAL** | **${{ $totalCost }}** | **{{ $totalLeadCount }}** |
@endcomponent
<br />
This summary includes all delivered and chargeable product assignments from {{ now()->startOfMonth()->format('F j') }} to {{ now()->format('F j, Y') }}.

If you have any questions about this report, please contact your account manager.

Kind Regards,<br>
The RoofingCalculator Team
@endcomponent
