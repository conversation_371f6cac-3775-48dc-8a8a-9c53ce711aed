@php
    /**
    * @var \App\Models\Odin\CompanyUser $companyUser
    * @var \App\DTO\Billing\PaymentMethods\PaymentMethodDTO $paymentMethod
    * @var bool $expired
    */
@endphp

@component('vendor.mail.html.message')
    <p style="text-align: center;">
        <img src="{{ asset('/images/FIXR_Logo.png') }}" style="width:50%" alt="App Logo">
    </p>
    @component('vendor.mail.html.subcopy')
        Dear {{ $companyUser->first_name }}, <br><br>
        We wanted to let you know that your card ending in {{ $paymentMethod->getLast4() }} {{ $expired ? 'has expired' : 'is expiring soon' }}.
        To ensure uninterrupted service and to continue receiving leads, please update this card or add a new one at your earliest convenience.<br><br>
        If you need any help, feel free to reach out to our Customer Support team.<br><br>
        Thank you for your prompt attention to this matter.
        @component('mail::button', ['url' => 'https://dashboard.fixr.com'])
            View Dashboard
        @endcomponent
    @endcomponent
    @component('vendor.mail.html.subcopy')
        **Regards,**<br>
        The Fixr Team<br/>
    @endcomponent
@endcomponent



