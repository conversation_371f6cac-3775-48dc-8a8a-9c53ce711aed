<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <title></title>
    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<![endif]-->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type="text/css">
        #outlook a {
            padding: 0;
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
            max-width: 100%;
        }

        p {
            display: block;
            margin: 13px 0;
        }

        #header img {
            max-height: 75px;
        }

        #footer img {
            max-height: 75px;
        }

    </style>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:AllowPNG/>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <!--[if lte mso 11]>
    <style type="text/css">
        .mj-outlook-group-fix { width:100% !important; }
    </style>
    <![endif]-->
    <!--[if !mso]><!-->
    <link href="https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700" rel="stylesheet" type="text/css">
    <style type="text/css">
        @import url(https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700);

    </style>
    <!--<![endif]-->
    <style type="text/css">
        .show-on-mobile {
            display: block !important;
        }

        .show-on-desktop {
            display: none !important;
        }
        @media only screen and (min-width:480px) {
            .show-on-mobile {
                display: none !important;
            }

            .show-on-desktop {
                display: block !important;
            }

            .mj-column-px-570 {
                width: 570px !important;
                max-width: 570px;
            }

            .mj-column-px-510 {
                width: 510px !important;
                max-width: 510px;
            }

            .mj-column-per-29 {
                width: 29% !important;
                max-width: 29%;
            }

            .mj-column-per-69 {
                width: 69% !important;
                max-width: 69%;
            }

            .opn-column-37 {
                width: 37% !important;
                max-width: 37%;
            }

            .opn-column-63 {
                width: 63% !important;
                max-width: 63%;
            }

            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%;
            }

            .mj-column-px-300 {
                width: 300px !important;
                max-width: 300px;
            }

            .opn-h-125 {
                height: 125px !important;
            }

            .opn-px-25 {
                padding-left: 25px !important;
                padding-right: 25px !important;
            }

            .opn-show-desktop {
                display: block !important;
            }

            .opn-show-desktop-inline {
                display: inline !important;
            }

            .opn-hide-desktop {
                display: none !important;
            }

            .opn-w-50 {
                width: 50% !important;
            }

            .opn-pt-0 {
                padding-top: 0 !important;
            }
        }

    </style>
    <style media="screen and (min-width:480px)">
        .moz-text-html .mj-column-px-570 {
            width: 570px !important;
            max-width: 570px;
        }

        .moz-text-html .mj-column-px-510 {
            width: 510px !important;
            max-width: 510px;
        }

        .moz-text-html .mj-column-per-29 {
            width: 29% !important;
            max-width: 29%;
        }

        .moz-text-html .mj-column-per-69 {
            width: 69% !important;
            max-width: 69%;
        }

        .moz-text-html .mj-column-per-100 {
            width: 100% !important;
            max-width: 100%;
        }

        .moz-text-html .mj-column-px-300 {
            width: 300px !important;
            max-width: 300px;
        }

        .moz-text-html .opn-column-37 {
            width: 37% !important;
            max-width: 37%;
        }

        .moz-text-html .opn-column-63 {
            width: 63% !important;
            max-width: 63%;
        }

        .moz-text-html .opn-h-125 {
            height: 125px !important;
        }

        .moz-text-html .opn-px-25 {
            padding-left: 25px !important;
            padding-right: 25px !important;
        }

        .moz-text-html .opn-show-desktop {
            display: block !important;
        }

        .moz-text-html .opn-show-desktop-inline {
            display: inline !important;
        }

        .moz-text-html .opn-hide-desktop {
            display: none !important;
        }

        .moz-text-html .opn-w-50 {
            width: 50% !important;
        }

        .moz-text-html .opn-pt-0 {
            padding-top: 0 !important;
        }

        .button {
            font: bold 11px Arial;
            text-decoration: none;
            background-color: #EEEEEE;
            color: #333333;
            padding: 2px 6px 2px 6px;
            border-top: 1px solid #CCCCCC;
            border-right: 1px solid #333333;
            border-bottom: 1px solid #333333;
            border-left: 1px solid #CCCCCC;
        }

    </style>
    <style type="text/css">
        @media only screen and (max-width:479px) {
            .show-on-mobile {
                display: block !important;
            }

            .show-on-desktop {
                display: none !important;
            }

            table.mj-full-width-mobile {
                width: 100% !important;
            }

            td.mj-full-width-mobile {
                width: auto !important;
            }
        }

    </style>
</head>

<body style="word-spacing:normal;background-color:#edf2f7;">
<div style="background-color:#edf2f7;">
    <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:570px;" width="570"  bgcolor="#fafafa" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div id="header" style="background:#fafafa;background-color:#fafafa;margin:0px auto;padding:20px;max-width:570px;">
        {!! $header ?? '' !!}
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->

    <!-- Footer -->
    <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:570px;" width="570" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div style="background:white;background-color:white;margin:0px auto; padding:20px;max-width:570px;">
        {!! $content !!}
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->


    <!-- Footer -->
    <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:570px;" width="570" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
    <div id="footer" align="center" style="background:white;background-color:white;margin:0px auto; margin-top:-20px; padding:20px;max-width:570px;">
        {!! $footer ?? '' !!}
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
</div>
</body>

</html>
