@php
    $logo = data_get($componentsProps, 'invoice_logo');
@endphp
<style>
    {{--    Remove spaces added by puppeteer--}}
    #header, #footer {
        margin: 0 !important;
        padding: 0 !important;
        box-sizing: border-box;
    }
    * {
        font-family: -apple-system, blinkmacsystemfont, "Segoe UI", roboto, oxygen, ubuntu, cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    }

    header {
        width: 100% !important;
        display: flex;
        padding: 50px 50px 0;
        justify-content: space-between;
        height: 100px;
    }

    header div {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        font-family: Inter;
        gap: 3px;
    }

    .headerTitle {
        font-weight: 600;
        line-height: 120%; /* 26.4px */
        letter-spacing: -0.88px;
        font-size: 26px;
    }

    .content, .headerTitle {
        text-align: right;
    }

    .address {
        display: flex;
        flex-direction: column;
        gap: 2px;
        margin-top: 14px;
        font-size: 14px;
    }

</style>

<header>
    <div>
        @if($logo === 'fixr')
            <x-fixr-logo></x-fixr-logo>
        @else
            <x-sr-logo></x-sr-logo>
        @endif
        <div class="address">
            <span>{{data_get($componentsProps, 'invoice_contact_address.line_one')}}</span>
            <span>{{data_get($componentsProps, 'invoice_contact_address.line_two')}}</span>
            <span>
                {{
                    data_get($componentsProps, 'invoice_contact_address.city')
                    . ' ' . data_get($componentsProps, 'invoice_contact_address.state')
                    . ' ' . data_get($componentsProps, 'invoice_contact_address.post_code')
                }}
            </span>
        </div>
    </div>

    <div>
        <span class="headerTitle">Invoice</span>
        <span class="content"><strong>Invoice:</strong> #{{$invoiceId}}</span>
        <span class="content"><strong>Issue Date:</strong> {{$issueDate}}</span>
        <span class="content"><strong>Due Date:</strong> {{$dueDate}}</span>
    </div>
</header>



