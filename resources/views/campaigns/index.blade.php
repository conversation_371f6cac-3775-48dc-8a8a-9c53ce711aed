@php
	use App\Models\Odin\Industry;
	use App\Models\Campaigns\CompanyCampaign;
@endphp
@php
	/**
	* @var CompanyCampaign $campaign
	*/
@endphp
<x-app-layout title="Campaigns">
	<div class="p-10">
		<form method="GET" action="{{ route('experimental.campaigns.index') }}">
			<label for="default-search"
			       class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Search</label>
			<div class="relative">
				<div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
					<svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true"
					     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
						<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
						      d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
					</svg>
				</div>
				<input value="{{ $zip_code }}" name="zip_code" type="search" id="default-search"
				       class="block w-full p-4 ps-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
				       placeholder="Zip Code"/>
			</div>

			<div class="mt-5">
				<label for="industries" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Select an
					industry</label>
				<ul class="z-10 bg-white rounded-lg shadow-sm h-48 px-3 pb-3 overflow-y-auto text-sm text-gray-700 dark:text-gray-200 dark:bg-gray-700">
					@foreach($industryOptions as $industry)
						<li>
							<div class="flex items-center p-2 rounded-sm hover:bg-gray-100 dark:hover:bg-gray-600">
								<input name="industries[]" type="checkbox" value="{{ $industry->id }}"
								       class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500" @checked(collect($industries)->some(fn (int $id) => $id === $industry->id))>
								<label for="industries[]"
								       class="w-full ms-2 text-sm font-medium text-gray-900 rounded-sm dark:text-gray-300">
									{{ $industry->name }}
								</label>
							</div>
						</li>
					@endforeach
				</ul>
			</div>

			<div class="my-5">
				<button type="submit"
				        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
					Apply Filters
				</button>

				<input class="focus:outline-none text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
				       type="reset">
			</div>
		</form>
		@if($zip_code)
			<div class="relative overflow-x-auto mt-5">
				<table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
					<thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
					<tr>
						<th scope="col" class="px-6 py-3">Name</th>
						<th scope="col" class="px-6 py-3">Company</th>
						<th scope="col" class="px-6 py-3">Service and Industry</th>
						<th scope="col" class="px-6 py-3">Date of Last Lead Purchased</th>
					</tr>
					</thead>
					<tbody>
					@forelse($campaigns->items() as $campaign)
						<tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
							<td scope="row"
							    class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">{{ $campaign->name }}</td>
							<td class="px-6 py-4"><a
										class="font-medium text-blue-600 dark:text-blue-500 hover:underline"
										href="{{ route('companies.index', ['id' => $campaign->company->id, 'tab' => 'Campaigns']) }}"
										target="_blank">{{ $campaign->company->name }}</a></td>
							<td class="px-6 py-4">{{ $campaign->service->name }}&nbsp;-&nbsp;{{$campaign->service->industry->name }}</td>
							<td class="px-6 py-4">{{ $campaign->date_of_last_lead_purchased?->format('F j, Y g:i a T') ?? 'N/A' }}</td>
						</tr>
					@empty
						<tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
							<td colspan="4" class="px-6 py-4 text-center">No campaigns found</td>
						</tr>
					@endforelse
					</tbody>
				</table>
			</div>
			<div class="text-center mt-5">
				{{ $campaigns->links() }}
			</div>
		@endif
	</div>
</x-app-layout>
