@if(\Illuminate\Support\Facades\Session::has(\App\Services\ImpersonateService::SESSION_KEY))
    <div class="relative h-9">
    </div>
    <div class="bg-primary-800 fixed w-full @if(app()->environment('staging')) top-9 @else top-0 @endif  inset-x-0 h-9 z-50">
        <div class="absolute w-full h-9 bg-primary-500 left-0 top-0 animate-pulse z-10"></div>
        <div class="flex h-full justify-center items-center px-3 sm:px-6 lg:px-8 z-20 relative">
            <p class="font-medium text-white text-center text-sm">
                <span class="md:hidden">You're currently impersonating {{ \Illuminate\Support\Facades\Auth::user()->name }}</span>
                <span class="hidden md:inline">You're currently impersonating {{ \Illuminate\Support\Facades\Auth::user()->name }}</span>
            </p>
            <div class="absolute inset-y-0 right-0 mr-2 md:mr-8">
                <button type="button" class="flex p-2 rounded-md font-medium text-sm text-white focus:outline-none focus:ring-2 focus:ring-white">
                    <a href="{{ url("/impersonate/clear") }}">Stop</a>
                </button>
            </div>
        </div>
    </div>
@endif


