export default function useInvoiceTransactionHelper() {
    const TYPES = {
        INVOICE_CHARGE_SUCCESS: 'invoice_charge_success',
        INVOICE_CHARGE_FAILED: 'invoice_charge_failed',
        INVOICE_CHARGE_DISPUTE_OPENED: 'invoice_charge_dispute_opened',
        INVOICE_CHARGE_DISPUTE_CLOSED: 'invoice_charge_dispute_closed',
        INVOICE_CHARGE_REFUND_SUCCESS: 'invoice_charge_refund_success',
    }

    const PRESENTATION = {
        [TYPES.INVOICE_CHARGE_SUCCESS]: {
            badge_color: 'green'
        },
        [TYPES.INVOICE_CHARGE_FAILED]: {
            badge_color: 'red'
        },
        [TYPES.INVOICE_CHARGE_DISPUTE_OPENED]: {
            badge_color: 'amber'
        },
        [TYPES.INVOICE_CHARGE_DISPUTE_CLOSED]: {
            badge_color: 'gray'
        },
        [TYPES.INVOICE_CHARGE_REFUND_SUCCESS]: {
            badge_color: 'blue'
        },
    }

    return {
        TYPES,
        PRESENTATION,
    }
}