export default function useInvoiceActionRequestHelper() {
    const statuses = {
        pending: 'pending',
        approved: 'approved',
        rejected: 'rejected',
        cancelled: 'cancelled',
    }

    const titles = {
        [statuses.pending]: 'Pending',
        [statuses.approved]: 'Approved',
        [statuses.rejected]: 'Rejected',
        [statuses.cancelled]: 'Cancelled',
    }

    const types = {
        company: 'Company',
        invoice: 'Invoice',
    }

    const getRequestRelationType = (modelClass) => {
        if(modelClass === 'App\\Models\\Billing\\Invoice') {
            return types.invoice
        }
        else if(modelClass === 'App\\Models\\Odin\\Company') {
            return types.company
        }
        else {
            return modelClass
        }
    }

    const getStatusTitle = (status) => {
        return titles[status] ?? status
    }

    const getStatusesAsOptions = () => {
        return Object.values(statuses).map((status) => ({
            id: status,
            title: getStatusTitle(status),
            name: getStatusTitle(status)
        }))
    }

    return {
        statuses,
        titles,
        types,
        getStatusTitle,
        getStatusesAsOptions,
        getRequestRelationType
    }
}
