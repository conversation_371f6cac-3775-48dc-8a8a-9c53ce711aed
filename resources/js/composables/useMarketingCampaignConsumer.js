import {markRaw} from "vue";
import CustomInput from "../vue/components/Shared/components/CustomInput.vue";
import useSimpleIcon from "./useSimpleIcon.js";
import simpleIcon from "../vue/components/Mailbox/components/SimpleIcon.vue";

export default function useMarketingCampaignConsumer() {
    const simpleIcon = useSimpleIcon()

    const statuses = {
        INITIALISED: 'initialised',
        UPLOADED: 'uploaded',
        ARCHIVED: 'archived',
        ERROR: 'error',
        SENT: 'sent',
        QUEUED: 'queued',
        DELIVERED: 'delivered',
    }

    const styles = {
        [statuses.INITIALISED]: {
            badgeColor: 'blue',
        },
        [statuses.UPLOADED]: {
            badgeColor: 'green',
            badgeIcon: simpleIcon.icons.ARROW_UP_CIRCLE,
        },
        [statuses.ARCHIVED]: {
            badgeColor: 'grey',
            badgeIcon: simpleIcon.icons.ARCHIVE_BOX,
        },
        [statuses.ERROR]: {
            badgeColor: 'red',
            badgeIcon: simpleIcon.icons.EXCLAMATION_CIRCLE,
        },
        [statuses.SENT]: {
            badgeColor: 'blue',
            badgeIcon: simpleIcon.icons.PAPER_AIRPLANE,
        },
        [statuses.QUEUED]: {
            badgeColor: 'orange',
            badgeIcon: simpleIcon.icons.CLOCK,
        },
        [statuses.DELIVERED]: {
            badgeColor: 'green',
            badgeIcon: simpleIcon.icons.INBOX
        }
    }

    const titles = {
        [statuses.INITIALISED]: 'Initialised',
        [statuses.UPLOADED]: 'Uploaded',
        [statuses.ARCHIVED]: 'Archived',
        [statuses.ERROR]: 'Error',
        [statuses.SENT]: 'Sent',
        [statuses.QUEUED]: 'Queued',
        [statuses.DELIVERED]: 'Delivered',
    }

    const statusList = Object.values(statuses).map(status => ({id: status, name: titles[status]}))

    return {
        statuses,
        styles,
        titles,
        statusList,
    }
}
